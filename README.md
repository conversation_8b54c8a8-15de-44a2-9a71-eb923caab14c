# A股智能选股系统

基于Python的A股智能选股系统，采用分层架构设计，支持多数据源和多策略扩展。

## 项目状态

当前版本：**v0.1** - 基础框架 + 数据获取

## 功能特性

### v0.1 已实现功能
- ✅ 分层架构设计（接口抽象 + 具体实现）
- ✅ akshare数据源集成
- ✅ SQLite数据存储
- ✅ 股票基本信息管理
- ✅ 日K线数据获取和存储
- ✅ 完整的异常处理机制
- ✅ 日志系统
- ✅ 单元测试和集成测试

### 计划功能（后续版本）
- 🔄 v0.2: 交易量异动选股策略
- 🔄 v0.3: 定时任务 + 邮件通知
- 🔄 v0.4: 配置管理 + 性能优化
- 🔄 v0.5: 完善功能 + 部署支持

## 项目结构

```
select-in-ai/
├── README.md                   # 项目说明
├── requirements.txt            # 依赖包
├── src/                       # 源代码
│   ├── core/                  # 核心模块
│   │   ├── interfaces/        # 抽象接口
│   │   ├── models/           # 数据模型
│   │   └── exceptions/       # 自定义异常
│   ├── data/                 # 数据层
│   │   ├── sources/          # 数据源实现
│   │   └── access/           # 数据访问实现
│   ├── strategies/           # 选股策略（v0.2+）
│   ├── scheduler/            # 定时任务（v0.3+）
│   ├── notification/         # 通知模块（v0.3+）
│   ├── config/              # 配置管理（v0.4+）
│   ├── utils/               # 工具模块
│   └── main.py              # 主程序入口
├── tests/                   # 测试代码
├── scripts/                 # 脚本工具
├── config/                  # 配置文件
├── data/                    # 数据文件
├── logs/                    # 日志文件
└── doc/                     # 文档
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd select-in-ai

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置

#### MySQL配置（推荐）
```bash
# 1. 确保MySQL服务器运行在localhost:3306
# 2. 复制环境变量配置文件
cp .env.example .env

# 3. 编辑.env文件，设置MySQL连接参数
# DATABASE_TYPE=mysql
# MYSQL_HOST=localhost
# MYSQL_PORT=3306
# MYSQL_USER=root
# MYSQL_PASSWORD=your_password
# MYSQL_DATABASE=stock_selection
```

#### SQLite配置（备用）
```bash
# 在.env文件中设置
# DATABASE_TYPE=sqlite
# SQLITE_DB_PATH=data/stock_selection.db
```

### 3. 运行测试

```bash
# 运行v0.1版本测试（SQLite）
python scripts/test_v01.py

# 运行MySQL数据库测试
python scripts/test_mysql.py

# 测试新的数据更新功能
python scripts/test_data_update.py
```

### 4. 使用主程序

#### 基础功能
```bash
# 更新股票列表
python src/main.py --update-stocks

# 更新最近30天交易数据（限制前10只股票用于测试）
python src/main.py --update-data --days 30 --limit 10

# 显示统计信息
python src/main.py --stats

# 查看帮助
python src/main.py --help
```

#### 增量数据更新（新功能）
```bash
# 智能增量更新：自动检测需要更新的股票并获取缺失数据
python src/main.py --incremental
```

#### 历史数据补充（新功能）
```bash
# 补充所有股票最近90天的历史数据
python src/main.py --historical

# 补充指定时间范围的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical --stocks 000001,000002,600000 --start-date 2024-01-01

# 补充指定股票指定时间范围的历史数据
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

#### 功能组合使用
```bash
# 先更新股票列表，再进行增量数据更新
python src/main.py --update-stocks --incremental

# 更新股票列表后补充历史数据
python src/main.py --update-stocks --historical --start-date 2024-01-01
```

## 架构设计

### 核心设计原则
- **接口抽象**: 数据源、存储、策略都通过接口隔离
- **依赖隔离**: 各模块独立，便于测试和维护
- **可扩展性**: 支持新增数据源、存储方案和选股策略

### 主要接口
- `IDataSource`: 数据源抽象接口
- `IDataAccess`: 数据访问抽象接口
- `ISelectionStrategy`: 选股策略抽象接口（v0.2+）
- `INotifier`: 通知抽象接口（v0.3+）

## 开发计划

详细的开发计划请参考：[开发计划文档](doc/开发计划.md)

### 版本里程碑
- [x] **v0.1**: 基础框架 + 数据获取 ✅
- [ ] **v0.2**: 简单选股策略
- [ ] **v0.3**: 定时任务 + 通知
- [ ] **v0.4**: 配置管理 + 优化
- [ ] **v0.5**: 完善功能 + 部署

## 测试

### 运行测试
```bash
# v0.1版本功能测试
python scripts/test_v01.py

# 单元测试（后续版本）
pytest tests/unit/

# 集成测试（后续版本）
pytest tests/integration/
```

### 测试覆盖
- 数据源功能测试
- 数据访问功能测试
- 集成测试
- 异常处理测试

## 文档

- [产品需求文档 (PRD)](doc/PRD_A股选股系统.md)
- [架构设计文档](doc/架构设计文档.md)
- [开发计划](doc/开发计划.md)

## 技术栈

- **Python 3.8+**
- **数据获取**: akshare
- **数据库**: MySQL (主要), SQLite (备用)
- **数据处理**: pandas, numpy
- **测试**: pytest
- **代码质量**: flake8, black, mypy

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue。
