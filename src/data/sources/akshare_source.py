"""
akshare数据源实现
"""
import akshare as ak
import pandas as pd
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import time
import logging

from ...core.interfaces.data_source import IDataSource
from ...core.exceptions.custom_exceptions import DataSourceError


class AkshareDataSource(IDataSource):
    """akshare数据源实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.retry_times = 3
        self.retry_delay = 1  # 秒

    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "akshare"

    def get_stock_list(self) -> List[Dict]:
        """获取股票列表"""
        try:
            self.logger.info("开始获取股票列表...")

            # 获取A股股票列表
            stock_info = ak.stock_info_a_code_name()

            if stock_info is None or stock_info.empty:
                raise DataSourceError("获取股票列表失败：返回数据为空")

            result = []
            for _, row in stock_info.iterrows():
                stock_data = {
                    'stock_code': row['code'],
                    'stock_name': row['name'],
                    'industry': '',  # akshare基础接口不包含行业信息
                    'market': self._determine_market(row['code']),
                    'list_date': datetime.now()  # 暂时使用当前时间，后续可优化
                }
                result.append(stock_data)

            self.logger.info(f"成功获取股票列表，共 {len(result)} 只股票")
            return result

        except Exception as e:
            error_msg = f"获取股票列表失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataSourceError(error_msg)

    def get_daily_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """获取股票日K线数据"""
        try:
            self.logger.info(f"获取股票 {stock_code} 从 {start_date.date()} 到 {end_date.date()} 的数据")

            # 格式化日期
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            # 重试机制
            for attempt in range(self.retry_times):
                try:
                    # 获取日K线数据
                    df = ak.stock_zh_a_hist(
                        symbol=stock_code,
                        period="daily",
                        start_date=start_str,
                        end_date=end_str,
                        adjust=""
                    )

                    if df is None or df.empty:
                        self.logger.warning(f"股票 {stock_code} 在指定时间段内无数据")
                        return []

                    # 转换数据格式
                    result = []
                    for _, row in df.iterrows():
                        daily_data = {
                            'stock_code': stock_code,
                            'trade_date': pd.to_datetime(row['日期']),
                            'open_price': float(row['开盘']),
                            'close_price': float(row['收盘']),
                            'high_price': float(row['最高']),
                            'low_price': float(row['最低']),
                            'volume': int(row['成交量']),
                            'amount': float(row['成交额']),
                            'turnover_rate': float(row.get('换手率', 0)) if '换手率' in row else None
                        }
                        result.append(daily_data)

                    self.logger.info(f"成功获取股票 {stock_code} 数据，共 {len(result)} 条记录")
                    return result

                except Exception as e:
                    if attempt < self.retry_times - 1:
                        self.logger.warning(f"获取数据失败，第 {attempt + 1} 次重试: {str(e)}")
                        time.sleep(self.retry_delay)
                    else:
                        raise e

        except Exception as e:
            error_msg = f"获取股票 {stock_code} 日K线数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataSourceError(error_msg)

    def get_realtime_data(self, stock_codes: List[str]) -> List[Dict]:
        """获取实时数据"""
        try:
            self.logger.info(f"获取 {len(stock_codes)} 只股票的实时数据")

            result = []
            for stock_code in stock_codes:
                try:
                    # 获取实时数据
                    df = ak.stock_zh_a_spot_em()
                    stock_data = df[df['代码'] == stock_code]

                    if not stock_data.empty:
                        row = stock_data.iloc[0]
                        realtime_data = {
                            'stock_code': stock_code,
                            'stock_name': row['名称'],
                            'current_price': float(row['最新价']),
                            'change_percent': float(row['涨跌幅']),
                            'volume': int(row['成交量']),
                            'amount': float(row['成交额']),
                            'update_time': datetime.now()
                        }
                        result.append(realtime_data)

                    # 避免请求过于频繁
                    time.sleep(0.1)

                except Exception as e:
                    self.logger.warning(f"获取股票 {stock_code} 实时数据失败: {str(e)}")
                    continue

            self.logger.info(f"成功获取 {len(result)} 只股票的实时数据")
            return result

        except Exception as e:
            error_msg = f"获取实时数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataSourceError(error_msg)

    def validate_stock_code(self, stock_code: str) -> bool:
        """验证股票代码是否有效"""
        try:
            # 简单的格式验证
            if not stock_code or len(stock_code) != 6:
                return False

            # 检查是否为数字
            if not stock_code.isdigit():
                return False

            # 检查是否为A股代码范围
            code_int = int(stock_code)
            if (code_int >= 600000 and code_int <= 699999) or \
               (code_int >= 1 and code_int <= 399999):
                return True

            return False

        except Exception:
            return False

    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取交易日历（排除节假日和周末）"""
        try:
            self.logger.info(f"获取交易日历：{start_date.date()} 到 {end_date.date()}")

            # 使用akshare获取交易日历
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            # 重试机制
            for attempt in range(self.retry_times):
                try:
                    # 获取交易日历
                    trade_cal = ak.tool_trade_date_hist_sina()

                    if trade_cal is None or trade_cal.empty:
                        # 如果获取失败，使用简单的工作日计算（排除周末）
                        self.logger.warning("无法获取交易日历，使用简单工作日计算")
                        return self._get_workdays(start_date, end_date)

                    # 转换为datetime格式并筛选日期范围
                    trade_dates = pd.to_datetime(trade_cal['trade_date'])
                    filtered_dates = trade_dates[
                        (trade_dates >= start_date) & (trade_dates <= end_date)
                    ].tolist()

                    self.logger.info(f"成功获取交易日历，共 {len(filtered_dates)} 个交易日")
                    return filtered_dates

                except Exception as e:
                    if attempt < self.retry_times - 1:
                        self.logger.warning(f"获取交易日历失败，第 {attempt + 1} 次重试: {str(e)}")
                        time.sleep(self.retry_delay)
                    else:
                        # 最后一次重试失败，使用简单的工作日计算
                        self.logger.warning(f"获取交易日历失败，使用简单工作日计算: {str(e)}")
                        return self._get_workdays(start_date, end_date)

        except Exception as e:
            self.logger.error(f"获取交易日历异常: {str(e)}")
            return self._get_workdays(start_date, end_date)

    def _get_workdays(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取工作日（简单版本，排除周末）"""
        workdays = []
        current_date = start_date

        while current_date <= end_date:
            # 排除周末（周六=5，周日=6）
            if current_date.weekday() < 5:
                workdays.append(current_date)
            current_date += timedelta(days=1)

        return workdays

    def _determine_market(self, stock_code: str) -> str:
        """根据股票代码判断市场"""
        if not stock_code or len(stock_code) != 6:
            return "未知"

        code_int = int(stock_code)

        if code_int >= 600000 and code_int <= 699999:
            return "上海主板"
        elif code_int >= 1 and code_int <= 299999:
            return "深圳主板"
        elif code_int >= 300000 and code_int <= 399999:
            return "创业板"
        else:
            return "其他"
