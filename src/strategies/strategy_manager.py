"""
策略管理器
"""
import logging
from datetime import datetime
from typing import List, Dict, Type, Optional

from ..core.interfaces.strategy import ISelectionStrategy
from ..core.interfaces.data_access import IDataAccess
from ..core.models.selection_result import SelectionResult
from .volume_anomaly_strategy import VolumeAnomalyStrategy


class StrategyManager:
    """策略管理器"""
    
    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
        self.logger = logging.getLogger(__name__)
        self._strategies = {}
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """注册默认策略"""
        self.register_strategy('volume_anomaly', VolumeAnomalyStrategy)
    
    def register_strategy(self, name: str, strategy_class: Type[ISelectionStrategy]):
        """注册策略"""
        self._strategies[name] = strategy_class
        self.logger.info(f"注册策略: {name}")
    
    def get_available_strategies(self) -> List[str]:
        """获取可用策略列表"""
        return list(self._strategies.keys())
    
    def create_strategy(self, name: str) -> Optional[ISelectionStrategy]:
        """创建策略实例"""
        if name not in self._strategies:
            self.logger.error(f"未知策略: {name}")
            return None
        
        try:
            strategy_class = self._strategies[name]
            strategy = strategy_class()
            self.logger.info(f"创建策略实例: {strategy.get_strategy_name()}")
            return strategy
        except Exception as e:
            self.logger.error(f"创建策略{name}失败: {str(e)}")
            return None
    
    def execute_strategy(self, strategy_name: str, config: Dict = None, save_results: bool = True) -> List[Dict]:
        """执行指定策略"""
        try:
            self.logger.info(f"开始执行策略: {strategy_name}")
            
            # 创建策略实例
            strategy = self.create_strategy(strategy_name)
            if not strategy:
                raise ValueError(f"无法创建策略: {strategy_name}")
            
            # 设置配置
            if config:
                if strategy.validate_config(config):
                    strategy.set_config(config)
                    self.logger.info("策略配置已更新")
                else:
                    self.logger.warning("策略配置验证失败，使用默认配置")
            
            # 执行策略
            start_time = datetime.now()
            results = strategy.execute(self.data_access)
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            self.logger.info(f"策略执行完成，耗时{execution_time:.2f}秒，选中{len(results)}只股票")
            
            # 保存结果
            if save_results and results:
                self._save_results(strategy.get_strategy_name(), results)
            
            # 添加执行统计信息
            for result in results:
                result['execution_time'] = execution_time
                result['strategy_name'] = strategy.get_strategy_name()
            
            return results
            
        except Exception as e:
            self.logger.error(f"执行策略{strategy_name}失败: {str(e)}")
            raise
    
    def execute_multiple_strategies(self, strategy_configs: List[Dict], save_results: bool = True) -> Dict[str, List[Dict]]:
        """执行多个策略"""
        results = {}
        
        for strategy_config in strategy_configs:
            strategy_name = strategy_config.get('name')
            config = strategy_config.get('config', {})
            
            if not strategy_name:
                self.logger.warning("策略配置缺少名称，跳过")
                continue
            
            try:
                strategy_results = self.execute_strategy(strategy_name, config, save_results)
                results[strategy_name] = strategy_results
            except Exception as e:
                self.logger.error(f"执行策略{strategy_name}失败: {str(e)}")
                results[strategy_name] = []
        
        return results
    
    def _save_results(self, strategy_name: str, results: List[Dict]):
        """保存选股结果"""
        try:
            saved_count = 0
            for result in results:
                selection_result = {
                    'strategy_name': strategy_name,
                    'stock_code': result['stock_code'],
                    'selection_date': result['selection_date'],
                    'score': result['score'],
                    'reason': result['reason']
                }
                
                if self.data_access.save_selection_result(selection_result):
                    saved_count += 1
            
            self.logger.info(f"保存选股结果完成，共保存{saved_count}条记录")
            
        except Exception as e:
            self.logger.error(f"保存选股结果失败: {str(e)}")
    
    def get_strategy_results(self, strategy_name: str, selection_date: datetime = None) -> List[Dict]:
        """获取策略历史结果"""
        try:
            if selection_date is None:
                selection_date = datetime.now()
            
            results = self.data_access.get_selection_results(strategy_name, selection_date)
            self.logger.info(f"获取到{len(results)}条历史选股结果")
            return results
            
        except Exception as e:
            self.logger.error(f"获取策略结果失败: {str(e)}")
            return []
    
    def get_strategy_info(self, strategy_name: str) -> Optional[Dict]:
        """获取策略信息"""
        strategy = self.create_strategy(strategy_name)
        if not strategy:
            return None
        
        return {
            'name': strategy.get_strategy_name(),
            'description': strategy.get_strategy_description(),
            'config': strategy.get_config()
        }
    
    def validate_strategy_config(self, strategy_name: str, config: Dict) -> bool:
        """验证策略配置"""
        strategy = self.create_strategy(strategy_name)
        if not strategy:
            return False
        
        return strategy.validate_config(config)
    
    def format_results(self, results: List[Dict], show_details: bool = False) -> str:
        """格式化选股结果"""
        if not results:
            return "没有选股结果"
        
        lines = []
        lines.append("=" * 80)
        lines.append(f"选股结果 (共{len(results)}只股票)")
        lines.append("=" * 80)
        
        for i, result in enumerate(results, 1):
            stock_code = result.get('stock_code', '')
            stock_name = result.get('stock_name', '')
            score = result.get('score', 0)
            reason = result.get('reason', '')
            
            lines.append(f"{i:2d}. {stock_code} - {stock_name}")
            lines.append(f"    评分: {score:.2f}")
            lines.append(f"    原因: {reason}")
            
            if show_details:
                # 显示详细信息
                volume_multiplier = result.get('volume_multiplier')
                close_price = result.get('close_price')
                latest_volume = result.get('latest_volume')
                
                if volume_multiplier:
                    lines.append(f"    交易量放大: {volume_multiplier:.2f}倍")
                if close_price:
                    lines.append(f"    收盘价: {close_price:.2f}元")
                if latest_volume:
                    lines.append(f"    最新交易量: {latest_volume:,}手")
            
            lines.append("")
        
        # 添加统计信息
        if results and 'execution_time' in results[0]:
            execution_time = results[0]['execution_time']
            lines.append(f"执行时间: {execution_time:.2f}秒")
        
        lines.append("=" * 80)
        
        return "\n".join(lines)
