"""
数据源抽象接口定义
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from datetime import datetime


class IDataSource(ABC):
    """数据源抽象接口"""

    @abstractmethod
    def get_stock_list(self) -> List[Dict]:
        """
        获取股票列表

        Returns:
            List[Dict]: 股票列表，每个字典包含：
                - stock_code: 股票代码
                - stock_name: 股票名称
                - industry: 行业
                - market: 市场（主板、创业板等）
                - list_date: 上市日期
        """
        pass

    @abstractmethod
    def get_daily_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """
        获取股票日K线数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[Dict]: 日K线数据列表，每个字典包含：
                - stock_code: 股票代码
                - trade_date: 交易日期
                - open_price: 开盘价
                - close_price: 收盘价
                - high_price: 最高价
                - low_price: 最低价
                - volume: 成交量
                - amount: 成交额
                - turnover_rate: 换手率
        """
        pass

    @abstractmethod
    def get_realtime_data(self, stock_codes: List[str]) -> List[Dict]:
        """
        获取实时数据

        Args:
            stock_codes: 股票代码列表

        Returns:
            List[Dict]: 实时数据列表
        """
        pass

    @abstractmethod
    def validate_stock_code(self, stock_code: str) -> bool:
        """
        验证股票代码是否有效

        Args:
            stock_code: 股票代码

        Returns:
            bool: 是否有效
        """
        pass

    @abstractmethod
    def get_data_source_name(self) -> str:
        """
        获取数据源名称

        Returns:
            str: 数据源名称
        """
        pass

    @abstractmethod
    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        获取交易日历（排除节假日和周末）

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[datetime]: 交易日期列表
        """
        pass
